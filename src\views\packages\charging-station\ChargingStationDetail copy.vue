<template>
  <container class="charging-station-detail">
    <x-header :title="station.name || '充电站详情'">
      <x-button slot="left" type="back"></x-button>
      <x-button slot="right" type="text" @click="share">
        <van-icon name="share" />
      </x-button>
    </x-header>

    <content-view
      :status="status"
      :refresh-action="refreshAction"
      @refresh="refresh"
      @reload="reload"
    >
      <template v-if="status == AppStatus.READY">
        <!-- 基本信息 -->
        <div class="station-info">
          <div class="station-header">
            <h2 class="station-name">{{ station.name }}</h2>
            <div class="station-status">
              <span
                class="status-text"
                :class="{
                  'status-open': station.status === 'open',
                  'status-closed': station.status === 'closed',
                }"
              >
                {{ getStatusText(station.status) }}
              </span>
            </div>
          </div>

          <div class="station-address">
            <van-icon name="location-o" />
            <span>{{ station.address }}</span>
          </div>

          <div class="station-tags" v-if="station.tags && station.tags.length">
            <van-tag
              v-for="tag in station.tags"
              :key="tag"
              size="mini"
              :type="getTagType(tag)"
            >
              {{ tag }}
            </van-tag>
          </div>
        </div>

        <!-- 充电桩信息 -->
        <div class="charging-info">
          <div class="info-title">充电桩信息</div>
          <div class="info-grid">
            <div class="info-item" v-if="station.fastCharging">
              <div class="info-label">快充桩</div>
              <div class="info-value">
                {{ station.fastCharging.available }}/{{
                  station.fastCharging.total
                }}个
              </div>
            </div>
            <div class="info-item" v-if="station.slowCharging">
              <div class="info-label">慢充桩</div>
              <div class="info-value">
                {{ station.slowCharging.available }}/{{
                  station.slowCharging.total
                }}个
              </div>
            </div>
            <div class="info-item" v-if="station.powerRange">
              <div class="info-label">功率范围</div>
              <div class="info-value">{{ station.powerRange }}</div>
            </div>
            <div class="info-item" v-if="station.currentPrice">
              <div class="info-label">当前价格</div>
              <div class="info-value price">
                {{ station.currentPrice }}元/度
              </div>
            </div>
          </div>
        </div>

        <!-- 服务设施 -->
        <div
          class="services-info"
          v-if="station.services && station.services.length"
        >
          <div class="info-title">服务设施</div>
          <div class="services-grid">
            <div
              v-for="service in station.services"
              :key="service"
              class="service-item"
            >
              <van-icon :name="getServiceIcon(service)" />
              <span>{{ service }}</span>
            </div>
          </div>
        </div>

        <!-- 营业时间 -->
        <div class="business-hours" v-if="station.businessHours">
          <div class="info-title">营业时间</div>
          <div class="hours-content">{{ station.businessHours }}</div>
        </div>

        <!-- 评价信息 -->
        <div class="rating-info" v-if="station.rating">
          <div class="info-title">用户评价</div>
          <div class="rating-content">
            <van-rate
              v-model="station.rating"
              :size="16"
              :count="5"
              color="#ffd21e"
              void-color="#eee"
              readonly
            />
            <span class="rating-text">{{ station.rating }}</span>
            <span class="comment-count" v-if="station.commentCount">
              ({{ station.commentCount }}条评价)
            </span>
          </div>
        </div>

        <!-- 联系方式 -->
        <div class="contact-info" v-if="station.phone">
          <div class="info-title">联系方式</div>
          <div class="contact-item" @click="callPhone">
            <van-icon name="phone-o" />
            <span>{{ station.phone }}</span>
            <van-icon name="arrow" />
          </div>
        </div>
      </template>
    </content-view>

    <!-- 底部操作按钮 -->
    <div class="detail-footer" v-if="status == AppStatus.READY">
      <van-button
        class="action-btn"
        type="primary"
        size="large"
        @click="navigateToStation"
      >
        <van-icon name="guide-o" />
        导航前往
      </van-button>
    </div>
  </container>
</template>

<script>
import { mixinAuthRouter } from '@/mixins';
import { AppStatus } from '@/enums';
import { toast } from '@/bus';
import { getChargingStationDetail } from '@/api/modules/charging-station';
import { Icon, Tag, Rate, Button } from 'vant';
import {
  getStatusText,
  getTagType,
  getServiceIcon,
  generateNavigationUrl,
} from './utils';

export default {
  name: 'ChargingStationDetail',
  components: {
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [Rate.name]: Rate,
    [Button.name]: Button,
  },
  mixins: [mixinAuthRouter],
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      refreshAction: 0,
      station: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      const { id } = this.$route.params;
      if (!id) {
        toast().tip('充电站ID不能为空');
        this.$_router_back();
        return;
      }
      this.getStationDetail(id);
    },

    // 获取充电站详情
    getStationDetail(stationId) {
      this.status = AppStatus.LOADING;
      getChargingStationDetail(stationId)
        .then(res => {
          this.station = res;
          this.status = AppStatus.READY;
        })
        .catch(e => {
          console.error(e);
          this.status = AppStatus.ERROR;
          toast().tip(e);
        });
    },

    // 刷新
    refresh() {
      const { id } = this.$route.params;
      this.getStationDetail(id);
      this.refreshAction = Date.now();
    },

    // 重新加载
    reload() {
      this.init();
    },

    // 使用工具函数
    getStatusText,
    getTagType,
    getServiceIcon,

    // 拨打电话
    callPhone() {
      if (this.station.phone) {
        window.location.href = `tel:${this.station.phone}`;
      }
    },

    // 导航到充电站
    navigateToStation() {
      try {
        if (window.jsBridge && window.jsBridge.openMap) {
          window.jsBridge.openMap({
            latitude: this.station.lat,
            longitude: this.station.lng,
            name: this.station.name,
            address: this.station.address,
          });
        } else {
          const url = generateNavigationUrl(
            this.station.lng,
            this.station.lat,
            this.station.name
          );
          window.open(url);
        }
      } catch (error) {
        console.error('导航失败:', error);
        toast().tip('导航功能暂时不可用');
      }
    },

    // 分享
    share() {
      if (navigator.share) {
        navigator.share({
          title: this.station.name,
          text: `${this.station.name} - ${this.station.address}`,
          url: window.location.href,
        });
      } else {
        // 复制链接到剪贴板
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href);
          toast().tip('链接已复制到剪贴板');
        } else {
          toast().tip('分享功能暂不支持');
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.charging-station-detail {
  .station-info {
    background: #fff;
    padding: 16px;
    margin-bottom: 12px;

    .station-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .station-name {
        font-size: 18px;
        font-weight: 500;
        color: #333;
        margin: 0;
        flex: 1;
      }

      .station-status {
        .status-text {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 10px;

          &.status-open {
            background: #e8f5e8;
            color: #52c41a;
          }

          &.status-closed {
            background: #fff2f0;
            color: #ff4d4f;
          }
        }
      }
    }

    .station-address {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-size: 14px;
      color: #666;

      .van-icon {
        margin-right: 6px;
        color: #999;
      }
    }

    .station-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
    }
  }

  .charging-info,
  .services-info,
  .business-hours,
  .rating-info,
  .contact-info {
    background: #fff;
    padding: 16px;
    margin-bottom: 12px;

    .info-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
    }
  }

  .charging-info {
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .info-item {
        text-align: center;

        .info-label {
          font-size: 12px;
          color: #999;
          margin-bottom: 4px;
        }

        .info-value {
          font-size: 16px;
          font-weight: 500;
          color: #333;

          &.available {
            color: #52c41a;
          }

          &.price {
            color: #ff6b35;
          }
        }
      }
    }
  }

  .services-info {
    .services-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;

      .service-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        .van-icon {
          font-size: 24px;
          color: #1989fa;
          margin-bottom: 6px;
        }

        span {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }

  .business-hours {
    .hours-content {
      font-size: 14px;
      color: #333;
    }
  }

  .rating-info {
    .rating-content {
      display: flex;
      align-items: center;

      .rating-text {
        margin-left: 8px;
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }

      .comment-count {
        margin-left: 6px;
        font-size: 12px;
        color: #999;
      }
    }
  }

  .contact-info {
    .contact-item {
      display: flex;
      align-items: center;
      cursor: pointer;

      .van-icon:first-child {
        margin-right: 8px;
        color: #1989fa;
      }

      span {
        flex: 1;
        font-size: 14px;
        color: #333;
      }

      .van-icon:last-child {
        color: #999;
      }
    }
  }

  .detail-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px 16px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    z-index: 100;

    .action-btn {
      width: 100%;

      .van-icon {
        margin-right: 6px;
      }
    }
  }
}
</style>
