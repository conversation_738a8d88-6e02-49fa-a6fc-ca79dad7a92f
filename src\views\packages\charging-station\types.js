/**
 * 充电站相关类型定义
 */

/**
 * @typedef {Object} ChargingPileInfo
 * @property {number} total - 总数量
 * @property {number} available - 可用数量
 */

/**
 * @typedef {Object} ChargingStation
 * @property {string} id - 充电站ID
 * @property {string} name - 充电站名称
 * @property {string} address - 充电站地址
 * @property {number} lat - 纬度
 * @property {number} lng - 经度
 * @property {number} distance - 距离（米）
 * @property {'open'|'closed'|'maintenance'} status - 状态
 * @property {number} totalPiles - 总充电桩数
 * @property {number} availablePiles - 可用充电桩数
 * @property {ChargingPileInfo} fastCharging - 快充桩信息
 * @property {ChargingPileInfo} slowCharging - 慢充桩信息
 * @property {string} powerRange - 功率范围
 * @property {string} priceRange - 价格范围
 * @property {number} currentPrice - 当前价格
 * @property {number} rating - 评分
 * @property {number} commentCount - 评论数
 * @property {string[]} tags - 标签列表
 * @property {string[]} services - 服务设施列表
 * @property {string} businessHours - 营业时间
 * @property {string} phone - 联系电话
 */

/**
 * @typedef {Object} FilterCondition
 * @property {string} type - 筛选类型
 * @property {string[]} values - 筛选值列表
 */

/**
 * @typedef {Object} SearchParams
 * @property {number} page - 页码
 * @property {number} size - 每页数量
 * @property {number} lat - 纬度
 * @property {number} lng - 经度
 * @property {string} keyword - 搜索关键词
 * @property {'distance'|'rating'|'price'|'power'} sortBy - 排序方式
 * @property {FilterCondition[]} filters - 筛选条件
 */

/**
 * @typedef {Object} ApiResponse
 * @property {ChargingStation[]} list - 充电站列表
 * @property {number} total - 总数量
 * @property {number} page - 当前页码
 * @property {number} size - 每页数量
 * @property {boolean} hasMore - 是否有更多数据
 */

/**
 * @typedef {Object} FilterConfig
 * @property {string[]} voltage - 电压选项
 * @property {string[]} power - 功率选项
 * @property {string[]} businessHours - 营业时间选项
 * @property {string[]} highway - 高速模式选项
 * @property {string[]} parking - 停车场选项
 * @property {string[]} parkingFee - 停车费选项
 * @property {string[]} operationType - 运营类型选项
 * @property {string[]} stationStatus - 电站状态选项
 * @property {string[]} chargingType - 充电方式选项
 * @property {string[]} stationType - 电站类型选项
 * @property {string[]} services - 电站服务选项
 * @property {string[]} benefits - 权益选项
 */

/**
 * @typedef {Object} StationCategory
 * @property {number} id - 分类ID
 * @property {string} name - 分类名称
 * @property {number} count - 数量
 */

/**
 * @typedef {Object} SearchSuggestion
 * @property {string} id - 充电站ID
 * @property {string} name - 充电站名称
 * @property {string} address - 充电站地址
 */

/**
 * @typedef {Object} GeoLocation
 * @property {number} lat - 纬度
 * @property {number} lng - 经度
 */

/**
 * @typedef {Object} NavigationParams
 * @property {number} latitude - 纬度
 * @property {number} longitude - 经度
 * @property {string} name - 地点名称
 * @property {string} address - 地点地址
 */

// 导出类型（用于JSDoc）
export default {
  ChargingPileInfo: {},
  ChargingStation: {},
  FilterCondition: {},
  SearchParams: {},
  ApiResponse: {},
  FilterConfig: {},
  StationCategory: {},
  SearchSuggestion: {},
  GeoLocation: {},
  NavigationParams: {},
};
