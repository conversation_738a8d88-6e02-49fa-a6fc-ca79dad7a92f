import Vue from 'vue';
import { loading } from '@/bus';
// import Router from 'vue-router';
// import Router from '@/model/MyRouter';
import Router from '@/lib/vue-router.esm.js';
import { handleError } from './error-handler';
import AudioPlayer from '@/views/audio-player/router';
import Camera from '@pkg/camera/router';
import Emergency from '@pkg/emergency/router';
import MobileCharge from '@pkg/mobile-recharge/router';
import UGC from '@pkg/ugc/router';
import Account from './modules/account';
import Actions from './modules/actions';
import Address from './modules/address';
import ChargingStation from './modules/charging-station';
import app from './modules/app';
import Food from './modules/food';
import Forum from './modules/forum';
import Petroleum from './modules/fuelcard';
import GrabCoupons from './modules/grab-coupons';
import Inspection from './modules/inspection';
import AiTools from './modules/ai-tools';
import BlindDate from '@/views/blind-date/router';

import VehicleInspection from './modules/vehicle-inspection';
import Maintain from './modules/maintain';
import Mall from './modules/mall';
import MallG2 from './modules/mallg2';
import Order from './modules/order';
import CashierCheckout from './modules/payment';
import QRPay from './modules/qr-pay';
import Satisfaction from './modules/satisfaction';
import Ticket from './modules/ticket';
import Trips from './modules/trips';
import UnionPay from './modules/unionPay';
import vehicleDepartment from './modules/vehicleDepartment';
import MemberVIP from './modules/vip';
import MemberVIPV2 from './modules/vip-v2';
import VirtualGold from './modules/virtualGold';
import Wash from './modules/wash';
import WashCard from './modules/washcard';
import WashCardCombined from './modules/washcard-combined';
import WeChat from './modules/wechat';
import External from './modules/external';
import pageBridge from './modules/page-bridge';
import CarHelper from './modules/car-helper';
import Finance from './modules/finance';
import Media from './modules/media';
import CreditCard from './modules/credit-card';
import LiveBattle from './modules/live-battle';

Vue.use(Router);

const publicRoutes = [
  {
    path: '/',
    name: '养车首页',
    component(resolve) {
      import(/* webpackChunkName: "home" */ '@/views/Home')
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/test',
    name: 'test',
    component(resolve) {
      import(/* webpackChunkName: "test" */ '@/views/Test')
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/jglh/menus',
    name: 'JglhMenus',
    component(resolve) {
      import(
        /* webpackChunkName: "jglh-menus" */ '@/views/packages/jglh/JglhHomeMenus.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/jglh/search',
    name: 'JglhSearch',
    component(resolve) {
      import(
        /* webpackChunkName: "jglh-menus" */ '@/views/packages/search/JglhSearch.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
    children: [
      {
        path: 'results', // 结果页作为子路由
        name: 'SearchResults',
        component(resolve) {
          import(
            /* webpackChunkName: "jglh-menus" */ '@/views/packages/search/SearchResults.vue'
          )
            .then(resolve)
            .catch(handleError);
        },
        props: true,
      },
    ],
  },
  {
    path: '/news/insurance',
    name: 'NewsInsurance',
    component: resolve => {
      import(
        /* webpackChunkName: "news" */ '@/views/packages/news/NewsInsurance.vue'
      )
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/news',
    name: '新闻',
    component: resolve => {
      import(/* webpackChunkName: "news" */ '@/views/packages/news/News.vue')
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/news1',
    name: '新闻1',
    component: resolve => {
      import(/* webpackChunkName: "news" */ '@/views/packages/news/News1.vue')
        .then(resolve)
        .catch(handleError);
    },
  },
  {
    path: '/shop/:id/comment/:type?',
    name: '商家评论',
    component: resolve => {
      import(/* webpackChunkName: "shop-comments" */ '@/views/ShopComments')
        .then(resolve)
        .catch(handleError);
    },
  },
  /* {
    path: '/shops/map',
    name: '附近商家（地图）',
    component: resolve => {
      require(['../views/ShopsMap'], resolve);
    },
    meta: {
      keepAlive: true,
    },
  }, */
  {
    path: '/shop/:id/map',
    name: '商家位置',
    component: resolve => {
      import('@/views/ShopMap').then(resolve).catch(handleError);
    },
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/buy/confirm',
    name: 'confirm-order',
    component: resolve => {
      import(/* webpackChunkName: "order-confirm" */ '@/views/OrderConfirm.vue')
        .then(e => {
          resolve(e);
        })
        .catch(e => {
          handleError(e);
        });
    },
  },
  {
    path: '/buy/ticket/select',
    name: 'buy-ticket-select',
    component: resolve => {
      import(
        /* webpackChunkName: "order-ticket-select" */ '@/views/BuyTicketSelect.vue'
      )
        .then(e => {
          resolve(e);
        })
        .catch(e => {
          handleError(e);
        });
    },
  },
  {
    path: '/pay/result/:id',
    name: '支付结果',
    component: resolve => {
      import('../views/PayResult')
        .then(e => {
          resolve(e);
        })
        .catch(e => {
          handleError(e);
        });
    },
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/error',
    name: '错误',
    component: resolve => {
      import('@/views/Error')
        .then(e => {
          resolve(e);
        })
        .catch(e => {
          handleError(e);
        });
    },
  },
  {
    path: '*',
    name: 'landing',
    component: resolve => {
      import('@/views/Landing')
        .then(e => {
          resolve(e);
        })
        .catch(e => {
          handleError(e);
        });
    },
  },
];

const routes = [
  AudioPlayer,
  UGC,
  Camera,
  MobileCharge,
  ...publicRoutes,
  ...CashierCheckout,
  ...Account,
  ...WashCard,
  ...WashCardCombined,
  ...Order,
  ...Wash,
  ...Maintain,
  ...Inspection,
  ...AiTools,
  ...VehicleInspection,
  ...Mall,
  ...Address,
  ...ChargingStation,
  ...Ticket,
  ...Petroleum,
  ...Forum,
  ...MemberVIP,
  ...MemberVIPV2,
  ...WeChat,
  ...QRPay,
  ...Trips,
  ...VirtualGold,
  ...Food,
  ...MallG2,
  ...GrabCoupons,
  ...UnionPay,
  ...vehicleDepartment,
  ...Actions,
  ...Satisfaction,
  ...app,
  ...External,
  ...pageBridge,
  ...CarHelper,
  ...Finance,
  ...Media,
  ...CreditCard,
  ...LiveBattle,
  ...Emergency,
  ...BlindDate,
];

const routerPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(error => error);
};
const routerReplace = Router.prototype.replace;
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(error => error);
};
const router = new Router({
  mode: 'hash',
  /**
   * vue-router mode: 'hash' 模式优先通过监听popstate实现单页应用路由状态监听
   * 经测试，webkit内核的容器中存 popstate事件在window.onload之前不能正常触发
   * 这可能是webkit内核的Bug，因为Firefox下即使onload事件触发前，popstate也可以被触发
   * 为解决此问题已修改vue-router源码，指定hash模式下监听hashchange事件
   * 自2018年7月28日17:49:35，本项目使用修改后的vue-router版本
   */
  routes,
  scrollBehavior(to, from, savedPosition) {
    // return 期望滚动到哪个的位置
    // console.log(savedPosition);
    return {
      y: 0,
    };
  },
});

// 跳转前不做权限校验，否则当进入页面是如果session不存在，下一个页面就无法加载出来
router.beforeEach((to, from, next) => {
  // console.log('vue-beforeeach')
  doNext(to.path, next); // 确保一定要调用 next()
});
router.onError(e => {
  console.error(e);
});
const requestedPath = {};
router.afterEach(route => {
  // console.log(JSON.stringify(requestedPath));
  if (requestedPath[route.path] === 1) {
    requestedPath[route.path]++;
    loading(false);
    // console.log('first load end');
  }
  const isDev =
    process.env.NODE_ENV === 'development' || /192\./.test(location.hostname);
  try {
    const { aplus_queue } = window;
    if (aplus_queue && !isDev) {
      aplus_queue.push({
        action: 'aplus.sendPV',
        arguments: [
          {
            is_auto: false,
          },
        ],
      });
    }
    // 百度统计 - 使用设置 - 规则设置 - 单页设置 - 启用单页应用数据统计，百度统计会基于 History API 或者 hashchange 自动为 单页应用 记录页面 PV 日志，开发者无需在路由切换时手动埋点;
    // if (!isDev) {
    //   window._hmt = window._hmt || [];

    //   // 1. 设置 PV 属性
    //   // 注：您也可以在页面内相关生命周期函数内赋值，确保对应函数的执行时机早于 afterEach 中 _trackPageview 的调用即可
    //   // window._hmt.push(['_setPageviewProperty', {
    //   //     page_name: 'pageName',
    //   //     page_title: null    // 当属性值传入 null 时，其作用为删除此前设置的该 PV 属性
    //   // }]);

    //   // 2. 发送 PV 日志
    //   window._hmt.push(['_trackPageview', route.path]);
    // }
  } catch (error) {}
  // setTransitionBack();
  // back(false);
});

function doNext(path, next, options) {
  // if (window.disableBack) {
  //   next(false)
  //   return
  // };
  if (!requestedPath[path]) {
    // console.log('first load start ', path);
    requestedPath[path] = 1;
    loading(true);
  }
  next(options);
}

export default router;
