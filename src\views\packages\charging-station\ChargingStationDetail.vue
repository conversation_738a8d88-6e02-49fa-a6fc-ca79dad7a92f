<template>
  <container class="charging-station-detail">
    <x-header :title="'充电站详情'">
      <x-button slot="left" type="back"></x-button>
      <x-button slot="right" type="text" @click="share">
        <van-icon name="share" />
      </x-button>
    </x-header>

    <content-view
      :status="status"
      :refresh-action="refreshAction"
      @refresh="refresh"
      @reload="reload"
    >
      <template v-if="status == AppStatus.READY">
        <div v-if="stationImages.length" class="station-page">
          <!-- 顶部图片 -->
          <div class="station-photos">
            <biz-image
              v-for="(image, index) in stationImages"
              :key="index"
              class="station-photo"
              :src="image"
              @click="playStationPhotos(index)"
            >
            </biz-image>
          </div>

          <!-- 站点信息卡片 -->
          <div class="station-info">
            <h2 class="title">郑州北三环科华超充充电站</h2>
            <div class="sub-info">
              <span class="score">4.8分</span>
              <span class="success">刚刚有人成功充过电</span>
            </div>
            <div class="tags">
              <span class="tag">特来电权益</span>
              <span class="tag">自营</span>
              <span class="tag">对外开放</span>
              <span class="tag">露天1F</span>
              <span class="tag">PLUS会员</span>
              <span class="tag">超级快充</span>
              <span class="tag">近期最大443kW</span>
            </div>
            <div class="station-address">
              <div class="address-content">
                <div class="address-text">
                  <i
                    class="icon_jglh icon-a-sc-weizhidizhidingwei address-icon"
                  ></i>
                  <span class="address-label">{{
                    station.address || '河南省郑州市金水区北三环经三路西南角'
                  }}</span>
                </div>
                <div class="action-buttons">
                  <div
                    class="action-btn navigation-btn"
                    @click="navigateToStation"
                  >
                    <i class="icon_jglh icon-sc-daohang btn-icon"></i>
                    <span class="btn-text">导航</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 服务设施 -->
          <!-- <div class="services">
            <div class="service-item"><i class="icon">🅿️</i> 专用通道</div>
            <div class="service-item"><i class="icon">☕</i> 休息室</div>
            <div class="service-item"><i class="icon">💡</i> 场站照明</div>
          </div> -->

          <!-- 充电桩信息 -->
          <div class="charge-info">
            <div
              class="charge-box"
              :class="{ 'single-type': chargeTypes.length === 1 }"
            >
              <div
                v-for="chargeType in chargeTypes"
                :key="chargeType.type"
                class="charge-item"
                :class="chargeType.type"
              >
                <div class="type-tag">{{ chargeType.name }}</div>
                <div class="status">
                  空闲
                  <span class="available-count">{{ chargeType.available }}</span
                  >/{{ chargeType.total }}
                </div>
                <div class="power">最大功率 {{ chargeType.maxPower }}kW</div>
              </div>
            </div>
          </div>

          <!-- 价格信息 -->
          <div class="price-info">
            <h3>价格信息</h3>
            <div class="current-price">
              <span class="value">0.9000</span><span class="unit">元/度</span>
            </div>
            <div class="time-price">
              16:00开始 1.2100元/度 <span class="more">全部时段 ></span>
            </div>
            <div class="parking">
              🅿️ 停车参考价：<span class="free">停车免费</span>
            </div>
          </div>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import { mixinAuthRouter } from '@/mixins';
import { AppStatus, ImageType } from '@/enums';
import { toast } from '@/bus';
import { navigate, playPhotos } from '@/bridge';
import { getImageURL } from '@/common/image';
import { getChargingStationDetail } from '@/api/modules/charging-station';
import { Icon, Tag, Rate, Button } from 'vant';
import {
  getStatusText,
  getTagType,
  getServiceIcon,
  generateNavigationUrl,
} from './utils';
export default {
  name: 'StationPage',
  components: {
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [Rate.name]: Rate,
    [Button.name]: Button,
  },
  mixins: [mixinAuthRouter],
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      refreshAction: 0,
      station: {},
      // {{ AURA-X: Add - 充电站图片数据. Confirmed via 寸止 }}
      stationImages: [
        // 示例图片，实际应从API获取
        'https://via.placeholder.com/400x200/f0f0f0/666?text=充电站图片1',
        'https://via.placeholder.com/400x200/e8f4fd/1989fa?text=充电站图片2',
        'https://via.placeholder.com/400x200/fff5f4/fd4925?text=充电站图片3',
        'https://via.placeholder.com/400x200/f0fdf4/30c856?text=充电站图片4',
        'https://via.placeholder.com/400x200/fffaf5/ff9330?text=充电站图片5',
      ],
      // {{ AURA-X: Add - 充电桩类型数据. Confirmed via 寸止 }}
      chargeTypes: [
        {
          type: 'super',
          name: '超充',
          available: 0,
          total: 2,
          maxPower: 480,
        },
        {
          type: 'fast',
          name: '快充',
          available: 11,
          total: 22,
          maxPower: 240,
        },
        // 示例：慢充类型（可根据实际数据动态显示）
        // {
        //   type: 'slow',
        //   name: '慢充',
        //   available: 5,
        //   total: 10,
        //   maxPower: 60,
        // },
      ],
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      const { id } = this.$route.params;
      if (!id) {
        toast().tip('充电站ID不能为空');
        this.$_router_back();
        return;
      }
      this.getStationDetail(id);
    },

    // 获取充电站详情
    getStationDetail(stationId) {
      this.status = AppStatus.LOADING;
      getChargingStationDetail(stationId)
        .then(res => {
          this.station = res;
          this.status = AppStatus.READY;
        })
        .catch(e => {
          console.error(e);
          this.status = AppStatus.ERROR;
          toast().tip(e);
        });
    },

    // 刷新
    refresh() {
      const { id } = this.$route.params;
      this.getStationDetail(id);
      this.refreshAction = Date.now();
    },

    // 重新加载
    reload() {
      this.init();
    },

    // 使用工具函数
    getStatusText,
    getTagType,
    getServiceIcon,

    // 拨打电话
    callPhone() {
      if (this.station.phone) {
        window.location.href = `tel:${this.station.phone}`;
      }
    },

    // 导航到充电站
    navigateToStation() {
      // {{ AURA-X: Modify - 使用navigate方法，参考OnlineInspectionShop实现. Confirmed via 寸止 }}
      const { address, lat, lng, name } = this.station;
      navigate({
        address: address || '河南省郑州市金水区北三环经三路西南角',
        name: name || '充电站',
        longitude: lng,
        latitude: lat,
        callback() {},
      });
    },

    // {{ AURA-X: Add - 图片预览方法，参考OnlineInspectionShop实现. Confirmed via 寸止 }}
    // 预览图片通用方法
    playPhotos(images, initIndex = 0) {
      const photos = images.map(function (item, i) {
        return {
          title: `图片${i + 1}`,
          url: getImageURL ? getImageURL(item, ImageType.MEDIUM) : item,
        };
      });
      const option = {
        download: true,
        initIndex,
        photos,
      };
      playPhotos(option);
    },

    // 预览充电站图片
    playStationPhotos(index) {
      this.playPhotos(this.stationImages, index);
    },

    // 分享
    share() {
      if (navigator.share) {
        navigator.share({
          title: this.station.name,
          text: `${this.station.name} - ${this.station.address}`,
          url: window.location.href,
        });
      } else {
        // 复制链接到剪贴板
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href);
          toast().tip('链接已复制到剪贴板');
        } else {
          toast().tip('分享功能暂不支持');
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.station-page {
  background: #f5f6fa;
  min-height: 100%;
  color: #333;

  .station-photos {
    // {{ AURA-X: Modify - 固定尺寸横向滚动布局. Confirmed via 寸止 }}
    display: flex;
    position: relative;
    background: #eef2f7;
    padding: 8px;
    gap: 8px;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
      display: none;
    }

    .station-photo {
      flex-shrink: 0; /* 防止图片被压缩 */
      width: 160px; /* 固定宽度 */
      height: 120px; /* 固定高度 */
      border-radius: 8px;
      position: relative;
      overflow: hidden;
      cursor: pointer;

      .img-count {
        position: absolute;
        right: 8px;
        bottom: 8px;
        display: inline-block;
        min-width: 20px;
        height: 20px;
        background: rgba(0, 0, 0, 0.6);
        text-align: center;
        border-radius: 10px;
        line-height: 20px;
        font-size: 12px;
        color: #fff;
        padding: 0 6px;
        font-weight: 500;
      }

      // 图片加载失败时的占位样式
      &::before {
        content: '充电站图片';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 13px;
        color: #666;
        z-index: 1;
      }
    }
  }

  .station-info {
    background: #fff;
    margin: 10px;
    padding: 12px;
    border-radius: 8px;

    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 6px;
    }

    .sub-info {
      font-size: 13px;
      color: #666;
      margin-bottom: 8px;

      .score {
        margin-right: 10px;
      }
      .success {
        color: #4caf50;
      }
    }

    .tags {
      margin-bottom: 10px;
      .tag {
        display: inline-block;
        background: #f2f2f2;
        border-radius: 12px;
        padding: 2px 8px;
        font-size: 12px;
        margin: 2px 4px 2px 0;
      }
    }

    .station-address {
      // {{ AURA-X: Add - 参考OnlineInspectionShop优化地址显示和按钮样式. Confirmed via 寸止 }}
      padding: 12px 0;

      .address-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }

      .address-text {
        flex: 1;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        line-height: 1.4;

        .address-icon {
          font-size: 16px;
          color: #fd4925;
          margin-right: 8px;
          flex-shrink: 0;
        }

        .address-label {
          flex: 1;
          word-break: break-all;
        }
      }

      .action-buttons {
        display: flex;
        gap: 12px;
        margin-left: 16px;
        flex-shrink: 0;
      }

      .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-width: 48px;
        min-height: 48px;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.95);
          background: #e9ecef;
        }

        .btn-icon {
          width: 24px;
          height: 24px;
          font-size: 16px;
          color: #fd4925;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .btn-text {
          font-size: 11px;
          color: #666;
          font-weight: 500;
          line-height: 1;
        }

        &.navigation-btn:hover {
          background: #fff5f4;

          .btn-icon {
            color: #e63946;
          }
        }

        &.phone-btn:hover {
          background: #f0f9ff;

          .btn-icon {
            color: #0284c7;
          }

          .btn-text {
            color: #0284c7;
          }
        }
      }
    }
  }

  .services {
    background: #fff;
    margin: 0 10px 10px;
    padding: 10px;
    border-radius: 8px;
    display: flex;
    justify-content: space-around;
    font-size: 13px;

    .service-item {
      display: flex;
      align-items: center;
      .icon {
        margin-right: 4px;
      }
    }
  }

  .charge-info {
    background: #fff;
    margin: 0 10px 10px;
    padding: 12px;
    border-radius: 8px;

    .charge-box {
      display: flex;
      gap: 8px;

      // {{ AURA-X: Add - 单类型布局优化. Confirmed via 寸止 }}
      &.single-type {
        justify-content: center;

        .charge-item {
          max-width: 300px;
        }
      }

      .charge-item {
        flex: 1;
        padding: 16px 12px;
        border-radius: 12px;
        font-size: 13px;
        position: relative;
        border: 1px solid transparent;

        .type-tag {
          // {{ AURA-X: Add - 类型标签样式，类似tag+斜体. Confirmed via 寸止 }}
          display: inline-block;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: bold;
          font-style: italic;
          margin-bottom: 8px;
          color: #fff;
        }

        .status {
          margin-bottom: 6px;
          font-size: 14px;
          color: #666;

          // {{ AURA-X: Add - 空闲数量显示明显. Confirmed via 寸止 }}
          .available-count {
            font-size: 18px;
            font-weight: bold;
            margin: 0 2px;
          }
        }

        .power {
          font-size: 12px;
          color: #999;
        }
      }

      // {{ AURA-X: Add - 各类型主题色. Confirmed via 寸止 }}
      .super {
        background: linear-gradient(135deg, #fff5f6 0%, #ffe8ea 100%);
        border-color: #f93b51;

        .type-tag {
          background: #f93b51;
        }

        .status .available-count {
          color: #f93b51;
        }
      }

      .fast {
        background: linear-gradient(135deg, #fffaf5 0%, #ffedd5 100%);
        border-color: #ff9330;

        .type-tag {
          background: #ff9330;
        }

        .status .available-count {
          color: #ff9330;
        }
      }

      .slow {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-color: #30c856;

        .type-tag {
          background: #30c856;
        }

        .status .available-count {
          color: #30c856;
        }
      }
    }
  }

  .price-info {
    background: #fff;
    margin: 0 10px 10px;
    padding: 12px;
    border-radius: 8px;

    h3 {
      font-size: 14px;
      margin-bottom: 8px;
    }

    .current-price {
      font-size: 18px;
      color: #ff3300;
      font-weight: bold;

      .value {
        font-size: 20px;
      }
      .unit {
        font-size: 13px;
        margin-left: 4px;
      }
    }

    .time-price {
      margin-top: 6px;
      font-size: 13px;
      color: #666;

      .more {
        float: right;
        color: #1989fa;
      }
    }

    .parking {
      margin-top: 10px;
      font-size: 13px;
      .free {
        color: #4caf50;
        margin-left: 4px;
      }
    }
  }
}
</style>
