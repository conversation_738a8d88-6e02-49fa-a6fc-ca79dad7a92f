/**
 * 充电站相关常量定义
 */

// 本地存储键名
export const STORAGE_KEYS = {
  SEARCH_HISTORY: 'charging_station_search_history',
  FILTER_CACHE: 'charging_station_filter_cache',
  USER_LOCATION: 'charging_station_user_location',
  SORT_PREFERENCE: 'charging_station_sort_preference',
};

// 搜索历史最大数量
export const MAX_SEARCH_HISTORY = 10;

// 防抖延迟时间（毫秒）
export const DEBOUNCE_DELAY = 300;

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_SIZE: 10,
  MAX_SIZE: 50,
};

// 充电站状态
export const STATION_STATUS = {
  OPEN: 'open',
  CLOSED: 'closed',
  MAINTENANCE: 'maintenance',
};

// 充电站状态文本映射
export const STATION_STATUS_TEXT = {
  [STATION_STATUS.OPEN]: '营业中',
  [STATION_STATUS.CLOSED]: '停业中',
  [STATION_STATUS.MAINTENANCE]: '维护中',
};

// 排序方式
export const SORT_TYPES = {
  DISTANCE: 'distance',
  RATING: 'rating',
  PRICE: 'price',
  POWER: 'power',
};

// 排序选项
export const SORT_OPTIONS = [
  { name: '距离最近', value: SORT_TYPES.DISTANCE },
  { name: '评分最高', value: SORT_TYPES.RATING },
  { name: '价格最低', value: SORT_TYPES.PRICE },
  { name: '充电功率', value: SORT_TYPES.POWER },
];

// 标签类型映射
export const TAG_TYPE_MAP = {
  '24小时': 'success',
  '快充': 'primary',
  '慢充': 'warning',
  '超充': 'danger',
  '免费停车': 'success',
  '收费停车': 'default',
  '地上停车': 'default',
  '地下停车': 'default',
  '机场专用': 'primary',
  '景区': 'success',
  '高速路': 'warning',
};

// 服务设施图标映射
export const SERVICE_ICON_MAP = {
  '休息室': 'home-o',
  '便利店': 'shop-o',
  '洗车': 'brush-o',
  '场站照明': 'bulb-o',
  '免费WIFI': 'wifi',
  '简餐': 'food-o',
  '洗手间': 'location-o',
};

// 筛选维度
export const FILTER_DIMENSIONS = {
  VOLTAGE: 'voltage',
  POWER: 'power',
  BUSINESS_HOURS: 'businessHours',
  HIGHWAY: 'highway',
  PARKING: 'parking',
  PARKING_FEE: 'parkingFee',
  OPERATION_TYPE: 'operationType',
  STATION_STATUS: 'stationStatus',
  CHARGING_TYPE: 'chargingType',
  STATION_TYPE: 'stationType',
  SERVICES: 'services',
  BENEFITS: 'benefits',
};

// 筛选选项配置
export const FILTER_OPTIONS = {
  [FILTER_DIMENSIONS.VOLTAGE]: ['200V-500V', '700V以上'],
  [FILTER_DIMENSIONS.POWER]: ['15KW-50KW', '50KW-120KW', '120KW-300KW', '300KW-500KW'],
  [FILTER_DIMENSIONS.BUSINESS_HOURS]: ['24小时', '营业中', '不确定'],
  [FILTER_DIMENSIONS.HIGHWAY]: ['高速路充电桩', '靠近高速路充电桩'],
  [FILTER_DIMENSIONS.PARKING]: ['箱货', '大巴', '重卡', '地上', '地下'],
  [FILTER_DIMENSIONS.PARKING_FEE]: ['限时免费', '收费'],
  [FILTER_DIMENSIONS.OPERATION_TYPE]: ['自营', '非自营', '互联'],
  [FILTER_DIMENSIONS.STATION_STATUS]: ['营业中', '停业中'],
  [FILTER_DIMENSIONS.CHARGING_TYPE]: ['直流快充', '直流慢充', '超级快充', '交流快充', '交流慢充'],
  [FILTER_DIMENSIONS.STATION_TYPE]: ['对外开放', '不对外开放'],
  [FILTER_DIMENSIONS.SERVICES]: ['休息室', '便利店', '洗车', '场站照明', '免费WIFI', '简餐', '洗手间'],
  [FILTER_DIMENSIONS.BENEFITS]: ['特来电', '即插即充', 'V2G'],
};

// 筛选维度标题
export const FILTER_TITLES = {
  [FILTER_DIMENSIONS.VOLTAGE]: '电压',
  [FILTER_DIMENSIONS.POWER]: '充电功率',
  [FILTER_DIMENSIONS.BUSINESS_HOURS]: '营业时间',
  [FILTER_DIMENSIONS.HIGHWAY]: '高速模式',
  [FILTER_DIMENSIONS.PARKING]: '停车场',
  [FILTER_DIMENSIONS.PARKING_FEE]: '停车费',
  [FILTER_DIMENSIONS.OPERATION_TYPE]: '运营类型',
  [FILTER_DIMENSIONS.STATION_STATUS]: '电站状态',
  [FILTER_DIMENSIONS.CHARGING_TYPE]: '充电方式',
  [FILTER_DIMENSIONS.STATION_TYPE]: '电站类型',
  [FILTER_DIMENSIONS.SERVICES]: '电站服务',
  [FILTER_DIMENSIONS.BENEFITS]: '权益',
};

// 地图导航配置
export const NAVIGATION_CONFIG = {
  // 高德地图导航URL模板
  AMAP_URL_TEMPLATE: 'https://uri.amap.com/navigation?to={lng},{lat},{name}&mode=car&policy=1&src=myapp&coordinate=gaode&callnative=0',
  
  // 百度地图导航URL模板
  BAIDU_URL_TEMPLATE: 'https://api.map.baidu.com/direction?destination={lat},{lng}&mode=driving&region=全国&output=html&src=myapp',
  
  // 腾讯地图导航URL模板
  TENCENT_URL_TEMPLATE: 'https://apis.map.qq.com/uri/v1/routeplan?type=drive&to={name}&tocoord={lat},{lng}&policy=1&referer=myapp',
};

// 错误消息
export const ERROR_MESSAGES = {
  LOCATION_DENIED: '获取位置失败，请检查定位权限',
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  DATA_LOAD_ERROR: '数据加载失败，请稍后重试',
  NAVIGATION_ERROR: '导航功能暂时不可用',
  SEARCH_ERROR: '搜索失败，请重试',
  FILTER_ERROR: '筛选失败，请重试',
};

// 成功消息
export const SUCCESS_MESSAGES = {
  LOCATION_SUCCESS: '定位成功',
  DATA_REFRESH_SUCCESS: '数据刷新成功',
  FILTER_APPLIED: '筛选条件已应用',
  SEARCH_SUCCESS: '搜索完成',
};

// 加载状态文本
export const LOADING_MESSAGES = {
  GETTING_LOCATION: '正在获取位置...',
  LOADING_DATA: '正在加载数据...',
  SEARCHING: '正在搜索...',
  FILTERING: '正在筛选...',
  REFRESHING: '正在刷新...',
};

// 空状态文本
export const EMPTY_MESSAGES = {
  NO_STATIONS: '暂无充电站数据',
  NO_SEARCH_RESULTS: '未找到相关充电站',
  NO_FILTER_RESULTS: '没有符合条件的充电站',
  NO_HISTORY: '暂无搜索历史',
};

// API超时配置
export const API_TIMEOUT = {
  DEFAULT: 10000, // 10秒
  SEARCH: 5000,   // 5秒
  LOCATION: 15000, // 15秒
};

// 距离格式化阈值
export const DISTANCE_THRESHOLDS = {
  METER_TO_KM: 1000,      // 1000米以上显示公里
  PRECISION_THRESHOLD: 10000, // 10公里以上不显示小数
};

// 评分配置
export const RATING_CONFIG = {
  MAX_RATING: 5,
  MIN_RATING: 0,
  STEP: 0.1,
};

// 充电桩可用性状态
export const AVAILABILITY_STATUS = {
  AVAILABLE: 'available',
  LIMITED: 'limited',
  FEW: 'few',
  UNAVAILABLE: 'unavailable',
};

// 可用性阈值
export const AVAILABILITY_THRESHOLDS = {
  SUFFICIENT: 0.5,  // 50%以上为充足
  LIMITED: 0.2,     // 20%-50%为紧张
  FEW: 0.1,         // 10%-20%为稀少
  // 10%以下为暂无可用
};
