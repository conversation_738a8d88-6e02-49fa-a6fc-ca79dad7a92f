<template>
  <div class="charging-station-item" @click="$emit('click')">
    <div class="station-header">
      <div class="station-info">
        <h3 class="station-name">{{ station.name }}</h3>
        <div class="station-tags">
          <van-tag
            v-for="tag in displayTags"
            :key="tag"
            size="mini"
            :type="getTagType(tag)"
          >
            {{ tag }}
          </van-tag>
        </div>
      </div>
      <div class="station-distance">
        <span class="distance" v-if="station.distance">
          {{ formatDistance(station.distance) }}
        </span>
      </div>
    </div>

    <div class="station-content">
      <div class="station-details">
        <div class="detail-item" v-if="station.fastCharging">
          <span class="label">快充：</span>
          <span class="value"
            >{{ station.fastCharging.available }}/{{
              station.fastCharging.total
            }}个</span
          >
        </div>

        <div class="detail-item" v-if="station.slowCharging">
          <span class="label">慢充：</span>
          <span class="value"
            >{{ station.slowCharging.available }}/{{
              station.slowCharging.total
            }}个</span
          >
        </div>

        <div class="detail-item" v-if="station.currentPrice">
          <span class="label">价格：</span>
          <span class="value price">{{ station.currentPrice }}元/度</span>
        </div>
      </div>

      <div
        class="station-services"
        v-if="station.services && station.services.length"
      >
        <div class="services-label">服务设施：</div>
        <div class="services-list">
          <van-tag
            v-for="service in station.services.slice(0, 4)"
            :key="service"
            size="mini"
            plain
          >
            {{ service }}
          </van-tag>
          <span v-if="station.services.length > 4" class="more-services">
            等{{ station.services.length }}项
          </span>
        </div>
      </div>
    </div>

    <div class="station-footer">
      <div class="rating" v-if="station.rating">
        <van-rate
          :value="station.rating"
          :size="12"
          :count="5"
          color="#ffd21e"
          void-color="#eee"
          readonly
        />
        <span class="rating-text">{{ station.rating }}</span>
        <span class="comment-count" v-if="station.commentCount">
          ({{ station.commentCount }}条评价)
        </span>
      </div>

      <div class="action-buttons">
        <van-button
          size="mini"
          type="primary"
          plain
          @click.stop="$emit('navigate')"
        >
          导航
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { Tag, Icon, Rate, Button } from 'vant';
import { formatDistance, getTagType } from '../utils';

export default {
  name: 'ChargingStationItem',
  components: {
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Rate.name]: Rate,
    [Button.name]: Button,
  },
  props: {
    station: {
      type: Object,
      required: true,
    },
  },
  computed: {
    // 显示的标签（最多显示3个）
    displayTags() {
      if (!this.station.tags) return [];
      return this.station.tags.slice(0, 3);
    },
  },
  methods: {
    // 使用工具函数
    formatDistance,
    getTagType,
  },
};
</script>

<style lang="scss" scoped>
@import '~styles/mixin/index.scss';

.charging-station-item {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  .station-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .station-info {
      flex: 1;

      .station-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin: 0 0 8px 0;
        line-height: 1.4;
      }

      .station-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .van-tag {
          margin-right: 0;
        }
      }
    }

    .station-distance {
      .distance {
        font-size: 14px;
        color: #1989fa;
        font-weight: 500;
      }
    }
  }

  .station-content {
    .station-details {
      margin-bottom: 12px;

      .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        font-size: 13px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #999;
          min-width: 50px;
        }

        .value {
          color: #333;

          &.price {
            color: #ff6b35;
            font-weight: 500;
          }
        }

        .available {
          color: #52c41a;
          margin-left: 4px;
        }
      }
    }

    .station-services {
      .services-label {
        font-size: 12px;
        color: #999;
        margin-bottom: 6px;
      }

      .services-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 4px;

        .van-tag {
          margin-right: 0;
        }

        .more-services {
          font-size: 12px;
          color: #999;
          margin-left: 4px;
        }
      }
    }
  }

  .station-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;

    .rating {
      display: flex;
      align-items: center;

      .rating-text {
        margin-left: 6px;
        font-size: 13px;
        color: #333;
        font-weight: 500;
      }

      .comment-count {
        margin-left: 4px;
        font-size: 12px;
        color: #999;
      }
    }

    .action-buttons {
      .van-button {
        min-width: 60px;
      }
    }
  }
}
</style>
